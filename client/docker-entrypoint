#!/bin/sh -e

echo "Starting client container..."

# Validate required environment variables
required_vars="API_URL APP_NAME GOLD_API SENTRY_DSN"

for var in $required_vars; do
  eval value=\$$var
  if [ -z "$value" ]; then
    echo "Error: Required environment variable $var is not set"
    exit 1
  fi
done

echo "All required environment variables are set"

# Check if template file exists
if [ ! -f "/usr/share/nginx/html/env.template.js" ]; then
  echo "Error: env.template.js not found"
  ls -la /usr/share/nginx/html/
  exit 1
fi

# Generate env.js from template using envsubst
echo "Generating env.js from template..."
envsubst < /usr/share/nginx/html/env.template.js > /usr/share/nginx/html/env.js

# Verify env.js was created
if [ ! -f "/usr/share/nginx/html/env.js" ]; then
  echo "Error: Failed to create env.js"
  exit 1
fi

echo "env.js created successfully"
cat /usr/share/nginx/html/env.js

# Remove the template file for security
rm -f /usr/share/nginx/html/env.template.js

# Test nginx configuration
nginx -t

echo "Starting nginx..."
exec "${@}"
