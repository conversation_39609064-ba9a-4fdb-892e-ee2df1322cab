#!/bin/sh -e

# Validate required environment variables
required_vars="DATABASE_URL BETTER_AUTH_SECRET BETTER_AUTH_URL BETTER_AUTH_TRUSTED_ORIGINS XENDIT_API_KEY XENDIT_CALLBACK_TOKEN SENTRY_DSN"

for var in $required_vars; do
  eval value=\$$var
  if [ -z "$value" ]; then
    echo "Error: Required environment variable $var is not set"
    exit 1
  fi
done

echo "All required environment variables are set"

# Run database migrations (skip if SKIP_MIGRATIONS is set)
if [ "$SKIP_MIGRATIONS" != "true" ]; then
  echo "Running database migrations..."
  npx prisma migrate deploy
else
  echo "Skipping database migrations (SKIP_MIGRATIONS=true)"
fi

# Check if dist/utils/env.js exists
if [ ! -f "dist/utils/env.js" ]; then
  echo "Error: dist/utils/env.js not found"
  ls -la dist/
  ls -la dist/utils/ || echo "utils directory not found"
  exit 1
fi

echo "Starting server..."
echo "Process ID: $$"
echo "Current time: $(date)"
echo "Memory info:"
cat /proc/meminfo | head -3

# Check for existing lock file and clean if stale
if [ -f "/tmp/sumopod-server.lock" ]; then
  LOCK_PID=$(cat /tmp/sumopod-server.lock)
  if ! kill -0 "$LOCK_PID" 2>/dev/null; then
    echo "Removing stale lock file for PID $LOCK_PID"
    rm -f /tmp/sumopod-server.lock
  else
    echo "Another instance is running with PID $LOCK_PID"
    exit 1
  fi
fi

# Add startup delay if specified
if [ -n "$STARTUP_DELAY" ] && [ "$STARTUP_DELAY" -gt 0 ]; then
  echo "Waiting ${STARTUP_DELAY}ms before starting..."
  sleep $(echo "scale=3; $STARTUP_DELAY/1000" | bc -l)
fi

echo "Starting Node.js application..."
exec node dist/index.js
