import "dotenv/config";
import { getSentryDsn } from "./utils/env.js";

// Create a mock Sentry object to prevent crashes
let Sentry: any = {
  init: () => {},
  prismaIntegration: () => ({}),
  captureException: () => {},
  captureMessage: () => {},
  withScope: (callback: any) => callback({}),
  close: () => Promise.resolve(),
};

// Async function to initialize Sentry
async function initializeSentry() {
  try {
    // Check if we're running in Bun environment
    if (typeof Bun !== 'undefined') {
      Sentry = await import('@sentry/bun');
    } else {
      Sentry = await import('@sentry/node');
    }

    Sentry.init({
      dsn: getSentryDsn(),
      environment: process.env.NODE_ENV || 'development',
      tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      integrations: [
        // Prisma integration for database query tracing and error monitoring
        Sentry.prismaIntegration(),
      ],
      // Filter sensitive data from being sent to Sentry
      beforeSend(event: any) {
        // Remove sensitive headers
        if (event.request?.headers) {
          delete event.request.headers.authorization;
          delete event.request.headers.cookie;
        }
        return event;
      },
    });

    console.log('Sentry initialized successfully');
  } catch (error) {
    console.warn('Failed to load Sentry:', error);
  }
}



import { cors } from "hono/cors";
import { Hono } from "hono";
import { authMiddleware } from "./middleware/auth.js";
import { sentryErrorHandler, sentryTracingMiddleware } from "./middleware/sentry.js";
import authRoute from "./routes/auth.js";
import { createInvoice } from "./routes/create-invoice.js";
import dataRoute from "./routes/data.js";
import { xenditWebhook } from "./routes/xendit-webhook.js";
import { getCorsAllowHeaders, getCorsAllowMethods, getCorsOrigins, getPort } from "./utils/env.js";

const app = new Hono();

// Sentry middleware - must be first to catch all errors
app.use("*", sentryTracingMiddleware);
app.use("*", sentryErrorHandler);

// CORS Configuration from environment variables
const corsOrigins = getCorsOrigins();
const corsAllowHeaders = getCorsAllowHeaders();
const corsAllowMethods = getCorsAllowMethods();

app.use(
  "*",
  cors({
    origin: [...corsOrigins, "*", "null"],
    allowHeaders: corsAllowHeaders,
    allowMethods: corsAllowMethods,
    credentials: true,
  })
);


// Health check endpoints
app.get('/health', (c) => {
  return c.json({ status: 'alive', timestamp: new Date().toISOString() });
});

app.get('/ready', (c) => {
  const uptime = process.uptime();
  if (uptime < 10) {
    return c.json({ status: 'not ready', uptime }, 503);
  }
  return c.json({ status: 'ready', uptime });
});

app.get('/', (c) => {
  const memUsage = process.memoryUsage();
  const uptime = process.uptime();

  // Basic health indicators
  const isHealthy = uptime > 5 && memUsage.heapUsed < memUsage.heapTotal * 0.9;

  return c.json({
    status: isHealthy ? 'ok' : 'degraded',
    timestamp: new Date().toISOString(),
    uptime: uptime,
    version: '2.2.0',
    memory: {
      used: Math.round(memUsage.heapUsed / 1024 / 1024),
      total: Math.round(memUsage.heapTotal / 1024 / 1024),
      usage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
    },
    pid: process.pid
  });
});

app.route("/", authRoute);
app.route("/api/data", dataRoute);

app.use("/create-invoice", authMiddleware);

app.post("/create-invoice", createInvoice);
app.post("/xendit-webhook", xenditWebhook);

const port = getPort();

// Global error handlers for unhandled promise rejections and uncaught exceptions
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  Sentry.captureException(reason);
  // Don't exit on unhandled rejection in production to prevent restart loops
  if (process.env.NODE_ENV !== 'production') {
    process.exit(1);
  }
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  Sentry.captureException(error);
  // Don't exit immediately, let Sentry flush
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// Add process info logging
console.log(`Process started with PID: ${process.pid}`);
console.log(`Node.js version: ${process.version}`);
console.log(`Platform: ${process.platform}`);
console.log(`Memory usage:`, process.memoryUsage());

// Add startup delay to prevent rapid restarts
const startupDelay = parseInt(process.env.STARTUP_DELAY || '0');
if (startupDelay > 0) {
  console.log(`Waiting ${startupDelay}ms before starting server...`);
  await new Promise(resolve => setTimeout(resolve, startupDelay));
}

// Process lock to prevent multiple instances
const LOCK_FILE = '/tmp/sumopod-server.lock';

function createLockFile() {
  try {
    const fs = require('node:fs');
    if (fs.existsSync(LOCK_FILE)) {
      const lockContent = fs.readFileSync(LOCK_FILE, 'utf8');
      const lockPid = parseInt(lockContent);

      // Check if process is still running
      try {
        process.kill(lockPid, 0);
        console.error(`Another instance is already running with PID ${lockPid}`);
        process.exit(1);
      } catch {
        // Process not running, remove stale lock file
        console.log(`Removing stale lock file for PID ${lockPid}`);
        fs.unlinkSync(LOCK_FILE);
      }
    }

    // Create new lock file
    fs.writeFileSync(LOCK_FILE, process.pid.toString());
    console.log(`Created lock file with PID ${process.pid}`);

    // Remove lock file on exit
    process.on('exit', () => {
      try {
        if (fs.existsSync(LOCK_FILE)) {
          fs.unlinkSync(LOCK_FILE);
          console.log('Removed lock file');
        }
      } catch {
        // Ignore errors during cleanup
      }
    });
  } catch (error) {
    console.warn('Failed to create lock file:', error);
  }
}

// Async function to start the server
async function startServer() {
  // Create process lock first
  createLockFile();

  // Initialize Sentry
  await initializeSentry();

  console.log(`Server is running on port ${port}`);

  // For Node.js deployment
  if (typeof Bun === "undefined") {
    try {
      const { serve } = await import("@hono/node-server");
      const server = serve({
        fetch: app.fetch,
        port: Number(port),
      });

      console.log(`HTTP server started on port ${port}`);

      // Keep the process alive
      // Graceful shutdown handler
      let isShuttingDown = false;

      const gracefulShutdown = (signal: string) => {
        if (isShuttingDown) {
          console.log(`Already shutting down, ignoring ${signal}`);
          return;
        }

        isShuttingDown = true;
        console.log(`Received ${signal}, shutting down gracefully`);
        console.log(`Process uptime: ${process.uptime()} seconds`);

        // Set a timeout to force exit if graceful shutdown takes too long
        const forceExitTimeout = setTimeout(() => {
          console.error('Graceful shutdown timeout, forcing exit');
          process.exit(1);
        }, 10000); // 10 seconds timeout

        server.close(() => {
          console.log('HTTP server closed');
          clearTimeout(forceExitTimeout);

          // Flush Sentry before exiting if available
          if (Sentry && typeof Sentry.close === 'function') {
            console.log('Flushing Sentry...');
            Sentry.close(2000).then(() => {
              console.log('Sentry flushed, exiting');
              process.exit(0);
            }).catch((error: unknown) => {
              console.error('Sentry flush error:', error);
              process.exit(0);
            });
          } else {
            console.log('No Sentry to flush, exiting');
            process.exit(0);
          }
        });
      };

      process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
      process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }
}

// Start the server
startServer().catch((error) => {
  console.error('Failed to initialize server:', error);
  process.exit(1);
});

// Export app for type inference
export { app };

// For Bun/Cloudflare Workers
export default {
  port,
  fetch: app.fetch,
};