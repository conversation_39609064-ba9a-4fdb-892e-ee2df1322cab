# ===== SHARED LIBRARY BUILD STAGE =====
FROM node:20-alpine AS shared-builder

WORKDIR /app

# Install build dependencies in single layer with cleanup
RUN apk add --no-cache --virtual .build-deps python3 make g++ \
    && rm -rf /var/cache/apk/* /var/lib/apk/* /tmp/* /usr/share/man /usr/share/doc

# Copy shared package.json first for better caching
COPY shared/package.json shared/tsconfig.json ./shared/
WORKDIR /app/shared

# Install dependencies (include dev dependencies for build)
RUN npm install \
    && npm cache clean --force \
    && rm -rf ~/.npm /tmp/*

# Copy shared source and build with aggressive cleanup
COPY shared/src ./src
RUN npm run build \
    && echo '{"name":"shared","version":"1.0.0","main":"index.js","type":"module"}' > dist/package.json \
    && rm -rf node_modules src *.config.* tsconfig.json \
    && find . -name "*.map" -delete \
    && find . -name "*.env*" -type f -delete \
    && find . -name "*.test.*" -type f -delete \
    && find . -name "*.spec.*" -type f -delete \
    && find . -name "__tests__" -type d -exec rm -rf {} + 2>/dev/null || true

WORKDIR /app

# ===== SERVER BUILD STAGE =====
FROM node:20-alpine AS server-builder

WORKDIR /app

# Install build dependencies in single layer with cleanup
RUN apk add --no-cache --virtual .build-deps python3 make g++ openssl \
    && rm -rf /var/cache/apk/* /var/lib/apk/* /tmp/* /usr/share/man /usr/share/doc

# Copy built shared library from previous stage first
COPY --from=shared-builder /app/shared /app/shared

# Copy server package.json and tsconfig first for better caching
COPY server/package.json server/tsconfig.json ./server/
COPY tsconfig.json ./tsconfig.json

WORKDIR /app/server

# Replace workspace dependency with file path and install dependencies
RUN sed -i 's/"shared": "workspace:\*"/"shared": "file:..\/shared\/dist"/' package.json \
    && npm install \
    && npm cache clean --force \
    && rm -rf ~/.npm /tmp/*

# Copy server source and schema
COPY server/src ./src
COPY server/prisma ./prisma

# Generate Prisma client and build - keep all necessary files
RUN npx prisma generate \
    && npm run build \
    && ls -la dist/ \
    && ls -la dist/utils/ || echo "utils directory not found" \
    && find dist -name "*.js" -type f | head -10

# ===== PRODUCTION RUNTIME STAGE =====
FROM node:20-alpine AS production

# Install runtime dependencies and create user in single layer
RUN apk add --no-cache --virtual .runtime-deps openssl dumb-init tini \
    && rm -rf /var/cache/apk/* /var/lib/apk/* /tmp/* /usr/share/man /usr/share/doc /root/.cache \
    && addgroup -g 1001 -S nodejs \
    && adduser -S sumopod -u 1001 -G nodejs

WORKDIR /app

# Copy only essential production files with proper ownership
COPY --from=server-builder --chown=sumopod:nodejs /app/server/dist ./server/dist
COPY --from=server-builder --chown=sumopod:nodejs /app/server/package.json ./server/package.json
COPY --from=server-builder --chown=sumopod:nodejs /app/server/prisma ./server/prisma
COPY --from=server-builder --chown=sumopod:nodejs /app/server/node_modules/.prisma ./server/node_modules/.prisma
COPY --from=shared-builder --chown=sumopod:nodejs /app/shared ./shared

WORKDIR /app/server

# Replace workspace dependency with relative path and install production dependencies
RUN sed -i 's/"shared": "workspace:\*"/"shared": "file:..\/shared\/dist"/' package.json \
    && npm install --only=production \
    && npx prisma generate \
    && npm cache clean --force \
    && rm -rf ~/.npm /tmp/* /root/.cache \
    && ls -la dist/utils/ || echo "utils directory missing" \
    && ls -la node_modules/.prisma/ || echo "prisma client missing"

# Setup entrypoint and create necessary directories in single layer
COPY --chown=sumopod:nodejs server/docker-entrypoint /usr/local/bin/docker-entrypoint
RUN chmod +x /usr/local/bin/docker-entrypoint \
    && mkdir -p /app/logs /app/uploads \
    && chown -R sumopod:nodejs /app/logs /app/uploads \
    && chmod -R 755 /app/logs /app/uploads

# Security: Switch to non-root user
USER sumopod

# Robust health check with multiple endpoints
HEALTHCHECK --interval=45s --timeout=15s --start-period=90s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider --timeout=10 http://localhost:8080/health || \
        wget --no-verbose --tries=1 --spider --timeout=10 http://localhost:8080/ready || \
        exit 1

# Expose port
EXPOSE 8080

# Use tini for proper signal handling and process management
ENTRYPOINT ["tini", "--"]
CMD ["docker-entrypoint"]
