{"name": "server", "version": "0.0.1", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun run --hot src/index.ts", "start": "node dist/index.js"}, "dependencies": {"@hono/node-server": "^1.15.0", "@prisma/client": "^6.12.0", "@sentry/bun": "^9.39.0", "@sentry/node": "^9.39.0", "@supabase/supabase-js": "^2.50.3", "better-auth": "^1.2.12", "dotenv": "^17.0.1", "hono": "^4.8.4", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.0.10", "prisma": "^6.12.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}